<?php

namespace App\Services;

use App\Contracts\Interfaces\StudentRepositoryInterface;
use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Services\StudentEnrollmentService;
use App\Services\StudentRegistrationService;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

class StudentService
{
    /**
     * The student repository instance.
     */
    protected StudentRepositoryInterface $studentRepository;

    /**
     * The user repository instance.
     */
    protected UserRepositoryInterface $userRepository;

    /**
     * The student registration service instance.
     */
    protected StudentRegistrationService $studentRegistrationService;

    /**
     * The student enrollment service instance.
     */
    protected StudentEnrollmentService $studentEnrollmentService;

    /**
     * Create a new StudentService instance.
     */
    public function __construct(
        StudentRepositoryInterface $studentRepository,
        UserRepositoryInterface $userRepository,
        StudentRegistrationService $studentRegistrationService,
        StudentEnrollmentService $studentEnrollmentService
    ) {
        $this->studentRepository = $studentRepository;
        $this->userRepository = $userRepository;
        $this->studentRegistrationService = $studentRegistrationService;
        $this->studentEnrollmentService = $studentEnrollmentService;
    }

    /**
     * Get all students with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->studentRepository->getAll($filters);
    }



    /**
     * Get all active students.
     */
    public function getAllActiveStudents(): Collection
    {
        return $this->studentRepository->getAllActive();
    }

    /**
     * Find a student by ID.
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function findById(int $id): Student
    {
        try {
            return $this->studentRepository->findById($id);
        } catch (NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to NotFoundException
            throw new NotFoundException("Siswa dengan ID {$id} tidak ditemukan", null, [], [], 0, $e);
        }
    }



    /**
     * Create a new student with user account.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     */
    public function create(array $data): Student
    {
        return $this->studentRegistrationService->registerStudent($data);
    }



    /**
     * Update student data.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function update(int $id, array $data): bool
    {
        return $this->studentRegistrationService->updateStudent($id, $data);
    }



    /**
     * Enroll student to a classroom for a specific academic year.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function enrollStudentToClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        return $this->studentEnrollmentService->enrollStudentToClassroom($studentId, $classroomId, $academicYearId);
    }

    /**
     * Get student's classrooms.
     */
    public function getStudentClassrooms(int $studentId): Collection
    {
        return $this->studentEnrollmentService->getStudentClassrooms($studentId);
    }

    /**
     * Get student's performance data.
     */
    public function getStudentPerformance(int $studentId): array
    {
        try {
            return $this->studentRepository->getStudentPerformance($studentId);
        } catch (Throwable $e) {
            throw new DatabaseException('Gagal mendapatkan data performa siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get total number of students.
     */
    public function getTotalStudents(): int
    {
        return $this->studentRepository->count();
    }
}
