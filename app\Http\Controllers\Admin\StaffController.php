<?php

namespace App\Http\Controllers\Admin;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\StaffRequests\StaffFilterRequest;
use App\Http\Requests\StaffRequests\StaffStoreRequest;
use App\Http\Requests\StaffRequests\StaffUpdateRequest;
use App\Services\StaffService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StaffController extends Controller
{
    /**
     * StaffController constructor
     */
    public function __construct(protected StaffService $staffService)
    {
    }

    /**
     * Display a listing of staff members
     */
    public function index(StaffFilterRequest $request): View|JsonResponse
    {
        $staff = $this->staffService->getAll($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($staff);
        }

        return view('admin.pages.staff.index', [
            'statuses' => UserStatus::dropdown(),
            'roles' => [
                RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
            ],
        ]);
    }

    /**
     * Format response for DataTables
     */
    private function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name ?? '-')
            ->editColumn('email', fn($row) => $row->email ?? '-')
            ->editColumn('role', function ($row) {
                $role = $row->getRoleNames()->first();

                return match ($role) {
                    RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                    RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                    RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
                    default => $role ?? '-'
                };
            })
            ->editColumn('status', function ($row) {
                // Check if status is an object with color and label methods
                if (is_object($row->status) && method_exists($row->status, 'color') && method_exists($row->status, 'label')) {
                    return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
                }

                // If status is a string
                if (is_string($row->status)) {
                    $color = match ($row->status) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        'pending' => 'warning',
                        default => 'secondary'
                    };

                    return '<span class="badge bg-' . $color . ' text-uppercase">' . ucfirst($row->status) . '</span>';
                }

                // Default case
                return '<span class="badge bg-secondary text-uppercase">Unknown</span>';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at ? $row->created_at->format('d M Y') : '-';
            })
            ->addColumn('action', function ($row) {
                return view('admin.components.button-actions-v2', [
                    'id' => $row->id,
                    'edit' => route('admin.staff.edit', $row->id),
                    'destroy' => route('admin.staff.destroy', $row->id),
                ]);
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new staff member
     */
    public function create(): View
    {
        return view('admin.pages.staff.create', [
            'roles' => [
                RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
            ],
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created staff member
     */
    public function store(StaffStoreRequest $request): JsonResponse
    {
        try {
            $staff = $this->staffService->create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data staf berhasil dibuat',
                'data' => $staff,
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat membuat data staf', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified staff member
     */
    public function edit(int $id): View
    {
        try {
            $staff = $this->staffService->findById($id);

            return view('admin.pages.staff.edit', [
                'staff' => $staff,
                'roles' => [
                    RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                    RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                    RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
                ],
                'statuses' => UserStatus::dropdown(),
                'currentRole' => $staff->getRoleNames()->first(),
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified staff member
     */
    public function update(StaffUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $this->staffService->update($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data staf berhasil diperbarui',
            ]);
        } catch (BusinessLogicException $e) {
            // For business logic exceptions like 'no changes made'
            return response()->json([
                'success' => true,
                'message' => $e->getMessage(),
            ]);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat memperbarui data staf', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified staff member
     */
    public function destroy(int $id): JsonResponse
    {
        // dd($id);
        try {
            $this->staffService->delete($id);

            return response()->json([
                'success' => true,
                'message' => 'Data staf berhasil dihapus',
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat menghapus data staf', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Change staff member status
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|boolean',
            ]);

            $staff = $this->staffService->findById($id);
            $status = $request->status ? UserStatus::Active : UserStatus::Inactive;

            // Prevent changing own status
            if ($staff->id === auth()->id()) {
                return $this->errorResponse('Tidak dapat mengubah status akun sendiri.', Response::HTTP_BAD_REQUEST);
            }

            // Check if this is the last active admin
            if (!$request->status && $staff->hasRole(RoleEnum::ADMIN->value)) {
                $activeAdminCount = \App\Models\User::role(RoleEnum::ADMIN->value)->where('status', UserStatus::Active)->count();
                if ($activeAdminCount <= 1) {
                    return $this->errorResponse('Tidak dapat menonaktifkan admin terakhir.', Response::HTTP_BAD_REQUEST);
                }
            }

            $staff->status = $status;
            $staff->save();

            $statusLabel = $status === UserStatus::Active ? 'aktif' : 'nonaktif';

            return response()->json([
                'success' => true,
                'message' => "Status staf berhasil diubah menjadi {$statusLabel}",
            ]);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat mengubah status staf', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate standardized error response
     */
    protected function errorResponse(string $message, int $statusCode = Response::HTTP_BAD_REQUEST, ?array $errors = null, array $headers = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $statusCode, $headers);
    }
}
