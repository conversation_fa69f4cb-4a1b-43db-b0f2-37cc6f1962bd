<script type="text/javascript">
    let table; // Declare the variable in the global scope
    $(document).ready(function() {
        // Program filter is now loaded from server

        // Initialize DataTable
        table = $('#datatable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: "<?php echo e(route('admin.classrooms.index')); ?>",
                data: function(d) {
                    return {
                        ...d,
                        program_id: $('#program-filter').val(),
                        level: $('#level-filter').val(),
                        status: $('#status-filter').val(),
                        search: $('#search-input').val()
                    };
                },
                complete: function(response) {
                    $('#total-classrooms').text(response.responseJSON.recordsTotal || 0);
                }
            },
            columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false
                },
                {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'level_name',
                    name: 'level'
                },
                {
                    data: 'program_name',
                    name: 'program.name'
                },
                {
                    data: 'shift_name',
                    name: 'shift.name'
                },
                {
                    data: 'teacher_name',
                    name: 'teacher.user.name'
                },
                {
                    data: 'academic_year',
                    name: 'academicYear.year'
                },
                {
                    data: 'capacity_status',
                    name: 'capacity',
                    orderable: false,
                    searchable: false
                },
                {
                    data: 'status_label',
                    name: 'status'
                },
                {
                    data: 'academic_link',
                    name: 'academic_link',
                    orderable: false,
                    searchable: false
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false
                }
            ],
            language: {
                processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                searchPlaceholder: "Cari...",
                lengthMenu: "Tampilkan _MENU_ data",
                zeroRecords: "Data tidak ditemukan",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                infoFiltered: "(disaring dari _MAX_ total data)",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                }
            },
            order: [
                [1, 'asc']
            ]
        });

        // Adjust columns when the sidebar is toggled
        $('#topnav-hamburger-icon').on('click', function() {
            setTimeout(() => {
                table.columns.adjust().draw();
            }, 300);
        });

        // Filter changes event
        $('#program-filter, #level-filter, #status-filter').on('change', function() {
            table.draw();
        });

        // Search input keyup event
        $('#search-input').on('keyup', function(e) {
            if (e.key === 'Enter') {
                table.draw();
            }
        });

        // Search button click event
        $('#search-button').on('click', function() {
            table.draw();
        });

        // Export button click
        $('#export-btn').on('click', function() {
            Swal.fire({
                title: 'Coming Soon!',
                text: 'Fitur export akan segera tersedia.',
                icon: 'info'
            });
        });
    });

    // Delete functionality
    async function deleteItem(el) {
        try {
            const url = el.getAttribute('data-url');

            // Confirm deletion
            const result = await Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Anda yakin ingin menghapus kelas ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                reverseButtons: true
            });

            if (!result.isConfirmed) return;

            // Show loading
            Swal.fire({
                title: 'Memproses...',
                text: 'Sedang menghapus kelas',
                allowOutsideClick: false,
                allowEscapeKey: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Execute deletion
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Terjadi kesalahan saat menghapus data.');
            }

            // Show success alert
            await Swal.fire({
                title: 'Berhasil!',
                text: data.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

            // Refresh table
            table.ajax.reload(null, false);

        } catch (error) {
            await Swal.fire({
                title: 'Hapus Gagal',
                text: error.message,
                icon: 'error'
            });
        }
    }
</script>
<?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/classroom/scripts/index-script.blade.php ENDPATH**/ ?>